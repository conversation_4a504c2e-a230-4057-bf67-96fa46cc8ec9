"use client";
import { But<PERSON> } from "@/components/ui/button";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import "dayjs/locale/nb"; // Importerer norsk locale

dayjs.locale("nb"); // Setter locale til norsk

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const [clientInfo, setClientInfo] = useState<{
    pathname: string;
    search: string;
    userAgent: string;
    language: string;
    timestamp: string;
  } | null>(null);

  useEffect(() => {
    console.error(error);

    // Set client info only on client-side
    setClientInfo({
      pathname: window.location.pathname,
      search: window.location.search,
      userAgent: window.navigator.userAgent,
      language: window.navigator.language,
      timestamp: dayjs().format("D. MMMM [kl:] HH:mm")
    });
  }, [error]);

  return (
    <div className="mx-auto my-28 flex flex-col gap-6">
      <h1 className="text-5xl font-bold">Ooops! En feil har oppstått...</h1>
      <p>Beklager noe gikk galt. Prøv igjen senere.</p>
      <div className="w-full lg:w-2/3 p-6 bg-gray-100 border-red-600 border-2 grid grid-cols-[1fr_3fr] gap-2 rounded">
        <div>Tjeneste</div>
        <div className="font-semibold">PGS</div>
        <div>Side</div>
        <div className="font-semibold overflow-clip">
          {clientInfo?.pathname || "Loading..."}
          {clientInfo?.search || ""}
        </div>
        <div>Klokkeslett</div>
        <div className="font-semibold">
          {clientInfo?.timestamp || "Loading..."}
        </div>
        <div>Nettleser</div>
        <div className="font-semibold">{clientInfo?.userAgent || "Loading..."}</div>
        <div>Språkinnstilling</div>
        <div className="font-semibold">{clientInfo?.language || "Loading..."}</div>
        <div>FeilType</div>
        <div className="font-semibold">{error.name}</div>
        <div>Feilkode</div>
        <div>{error.digest}</div>
        {process.env.NODE_ENV === "development" && (
          <>
            <div>Feilmelding</div>
            <div>{error.message}</div>
          </>
        )}
      </div>

      <Button onClick={() => reset()} className="max-w-sm">
        Prøv igjen
      </Button>
    </div>
  );
}
