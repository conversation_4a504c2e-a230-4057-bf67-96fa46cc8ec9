import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface ConfirmDeleteModalProps {
  isOpen: boolean;
  fileName: string;
  onClose: () => void;
  onConfirm: () => void;
  confirmDeleteHeading: string;
  confirmDeleteMessage: string;
  confirmDeleteConfirmBtnLabel: string;
  cancelLabel: string;
}

function ConfirmDeleteModal({
  isOpen,
  onClose,
  onConfirm,
  fileName,
  confirmDeleteHeading,
  confirmDeleteMessage,
  confirmDeleteConfirmBtnLabel,
  cancelLabel: cancelLabel,
}: ConfirmDeleteModalProps) {
  if (!isOpen) return null;

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{confirmDeleteHeading}</AlertDialogTitle>
          <AlertDialogDescription className="py-4 break-words">
            <span>{confirmDeleteMessage}:</span>
            <span className="font-semibold ml-2">{fileName}</span> ?
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel asChild>
            <Button variant="ghost" onClick={onClose}>
              {cancelLabel}
            </Button>
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button onClick={onConfirm}>
              {confirmDeleteConfirmBtnLabel}
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

export default ConfirmDeleteModal;
