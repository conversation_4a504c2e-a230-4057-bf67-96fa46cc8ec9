import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Audience } from "@/app/enums/Audience";
import { useState } from "react";
import { v4 as uuidv4 } from "uuid";

const SendMessageToSignalR = ({}) => {
  const [candidateNumber, setCandidateNumber] = useState("");
  const [groupCode, setGroupCode] = useState("");
  const [messageId, setMessageId] = useState("");

  return null;
  return (
    <div className="App">
      <div className="App"></div>
      <div className="flex flex-col gap-10">
        <div className="flex flex-col gap-10">
          <div>
            <Button
              onClick={async () => {
                const message = {
                  id: uuidv4(),
                  message: "Hello from SignalR to all!",
                  audience: Audience.All,
                };
                const response = await fetch(
                  `${window.location.origin}/api/signalRMessages`,
                  {
                    method: "POST",
                    body: JSON.stringify(message),
                  }
                );

                console.log("Response from SignalR:", await response.json());
              }}
            >
              Send message to all
            </Button>
          </div>

          <div className="flex gap-4">
            <Input
              type="text"
              value={groupCode}
              onChange={(e) => setGroupCode(e.target.value)}
            />

            <Button
              onClick={async () => {
                const message = {
                  id: uuidv4(),
                  message: "Hello from SignalR to group!",
                  audience: Audience.Group,
                  audienceId: groupCode,
                };

                const response = await fetch(
                  `${window.location.origin}/api/signalRMessages`,
                  {
                    method: "POST",
                    body: JSON.stringify(message),
                  }
                );

                console.log(
                  "Response from SignalR:",
                  response.status,
                  response.statusText
                );
              }}
            >
              Send message to group
            </Button>
          </div>

          <div className="flex gap-4">
            <Input
              type="text"
              value={candidateNumber}
              onChange={(e) => setCandidateNumber(e.target.value)}
            />

            <Button
              onClick={async () => {
                const message = {
                  id: uuidv4(),
                  message: "Hello from SignalR to single user!",
                  audience: Audience.User,
                  audienceId: candidateNumber,
                };
                const response = await fetch(
                  `${window.location.origin}/api/signalRMessages`,
                  {
                    method: "POST",
                    body: JSON.stringify(message),
                  }
                );
              }}
            >
              Send message to single user
            </Button>
          </div>
          <div className="flex gap-4">
            <Input
              type="text"
              value={messageId}
              onChange={(e) => setMessageId(e.target.value)}
            />

            <Button
              onClick={async () => {
                const response = await fetch(
                  `${window.location.origin}/api/signalRMessages`,
                  {
                    method: "DELETE",
                    body: JSON.stringify({
                      id: messageId,
                    }),
                  }
                );

                console.log("Response from SignalR:", await response.json());
              }}
            >
              Delete message
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SendMessageToSignalR;
