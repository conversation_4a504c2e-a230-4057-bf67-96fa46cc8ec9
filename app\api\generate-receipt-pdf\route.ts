import { NextRequest, NextResponse } from "next/server";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { getHashFromRedis } from "@/app/lib/redisHelper";
import { logActivity } from "@/app/lib/logActivity";
import { OperationEnum } from "@/app/enums/OperationEnum";
import puppeteer from "puppeteer";

export async function POST(request: NextRequest) {
  try {
    // Autentisering og autorisering
    const userSessionData = await getUserSessionData();
    if (!userSessionData?.userSessionId) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Sjekk autorisering fra Redis
    const userKey = `candidate:${userSessionData.candidateNumber}:${userSessionData.userSessionId}`;
    const isAuthorized = await getHashFromRedis(userKey, "isAuthorized");

    if (isAuthorized !== "true") {
      return NextResponse.json(
        { message: "User not authorized" },
        { status: 403 }
      );
    }

    // Hent URL fra request body
    const { receiptUrl } = await request.json();
    if (!receiptUrl) {
      return NextResponse.json(
        { message: "Receipt URL is required" },
        { status: 400 }
      );
    }

    // Start Puppeteer
    const browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    });

    const page = await browser.newPage();

    // Sett viewport og andre innstillinger
    await page.setViewport({ width: 1200, height: 800 });
    
    // Naviger til kvitteringssiden
    await page.goto(receiptUrl, { 
      waitUntil: 'networkidle0',
      timeout: 30000 
    });

    // Vent på at innholdet er lastet
    await page.waitForSelector('#ExamReceipt', { timeout: 10000 });

    // Skjul elementer som ikke skal være med i PDF
    await page.addStyleTag({
      content: `
        [data-html2canvas-ignore="true"] {
          display: none !important;
        }
        .btn {
          display: none !important;
        }
        button {
          display: none !important;
        }
      `
    });

    // Generer PDF
    const pdfBuffer = await page.pdf({
      format: 'A4',
      margin: {
        top: '20mm',
        right: '20mm',
        bottom: '20mm',
        left: '20mm'
      },
      printBackground: true,
      preferCSSPageSize: false
    });

    await browser.close();

    // Logg aktivitet
    await logActivity("", "", OperationEnum.KvitteringNedlastet);

    // Returner PDF som response
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="Kvittering.pdf"',
        'Content-Length': pdfBuffer.length.toString(),
      },
    });

  } catch (error) {
    console.error("Error generating PDF with Puppeteer:", error);
    return NextResponse.json(
      { message: "Error generating PDF" },
      { status: 500 }
    );
  }
}
