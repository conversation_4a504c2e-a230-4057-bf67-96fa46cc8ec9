import { getTranslations } from "next-intl/server";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { StatusInfoEmptyError } from "@/app/lib/exceptionTypes";
import { getStatusInfoFromPgsa } from "@/app/lib/getStatusInfoFromPgsa";
import { RouteEnum } from "@/app/enums/RouteEnum";
import Link from "next/link";
import { FaArrowRight } from "react-icons/fa";
import { Button } from "@/components/ui/button";

const ExamInfo = async () => {
  const userSessionData = await getUserSessionData();

  const [statusInfo, t] = await Promise.all([
    getStatusInfoFromPgsa(userSessionData.userId),
    getTranslations("Eksamensoppgave"),
  ]);

  if (!statusInfo) throw new StatusInfoEmptyError();

  return (
    <div className="card rounded-none w-full bg-white">
      <div className="card-body">
        <h2 className="card-title font-normal text-base">
          {t("DeliverExamIn")}
          {statusInfo.SubjectName}
        </h2>
        <div className="flex gap-2 items-center mt-2">
          <span className="flex w-4 h-4 bg-secondary rounded-full" />

          <p>{t("ExamOpenToDeliver")}</p>
        </div>
        <div className="flex flex-col mt-4 gap-4">
          <Link href={RouteEnum.Levering}>
            <Button className="w-full sm:w-48">
              <span className="flex items-center gap-2">
                {t("DeliverExam")}
                <FaArrowRight role="img" aria-label="Pil høyre" />
              </span>
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ExamInfo;
