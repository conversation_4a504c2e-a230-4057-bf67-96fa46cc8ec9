"use client";

import { BiArrowToBottom } from "react-icons/bi";
import { useState } from "react";

interface Props {
  downloadRecieptLabel: string;
}

async function downloadReceiptWithPuppeteer() {
  try {
    // Få nåværende URL for kvitteringssiden
    const currentUrl = window.location.href;

    // Kall API-endepunktet for PDF-generering
    const response = await fetch('/api/generate-receipt-pdf', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        receiptUrl: currentUrl
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // Hent PDF som blob
    const pdfBlob = await response.blob();

    // Opprett download link
    const url = window.URL.createObjectURL(pdfBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'Kvittering.pdf';
    document.body.appendChild(link);
    link.click();

    // Rydd opp
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

  } catch (error) {
    console.error("Error downloading receipt with Puppeteer:", error);
    throw error;
  }
}

const DownloadButton = ({ downloadRecieptLabel }: Props) => {
  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownload = async () => {
    if (isDownloading) return;

    setIsDownloading(true);
    try {
      await downloadReceiptWithPuppeteer();
    } catch (error) {
      console.error("Failed to download receipt:", error);
      // Her kan du legge til toast notification eller annen feilhåndtering
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <button
      className="btn btn-ghost mb-10"
      data-html2canvas-ignore="true"
      onClick={handleDownload}
      disabled={isDownloading}
    >
      <BiArrowToBottom size={22} role="img" aria-label="Nedlastingsikon" />
      <span>{isDownloading ? "Laster ned..." : downloadRecieptLabel}</span>
    </button>
  );
};

export default DownloadButton;
