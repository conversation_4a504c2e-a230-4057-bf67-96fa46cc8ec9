import { FaCircle } from "react-icons/fa";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface DeliverExamModalProps {
  ConfirmationFileDeliveryLabel: string;
  IConfirmLabel: string;
  FilesAreCorrectLabel: string;
  CannotEditAnswerLabel: string;
  ConfirmDeliveryLabel: string;
  CancelLabel: string;
  onConfirm: () => void;
  onClose: () => void;
  isOpen: boolean;
}

function DeliverExamModal({
  ConfirmationFileDeliveryLabel,
  IConfirmLabel,
  FilesAreCorrectLabel,
  CannotEditAnswerLabel,
  ConfirmDeliveryLabel,
  CancelLabel,
  onConfirm,
  onClose,
  isOpen,
}: DeliverExamModalProps) {
  if (!isOpen) return null;

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{ConfirmationFileDeliveryLabel}</AlertDialogTitle>
          <AlertDialogDescription className="py-4 break-words flex flex-col gap-4">
            <span className="font-semibold">{IConfirmLabel}:</span>
            <ul className="ml-2">
              <li className="flex gap-2 items-center flex-row">
                <FaCircle role="img" aria-label="liten sirkel" size={8} />
                {FilesAreCorrectLabel}
              </li>
              <li className="flex gap-2 items-center">
                <FaCircle role="img" aria-label="liten sirkel" size={8} />
                {CannotEditAnswerLabel}
              </li>
            </ul>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel asChild>
            <Button variant="ghost" onClick={onClose}>
              {CancelLabel}
            </Button>
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button onClick={onConfirm}>{ConfirmDeliveryLabel}</Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

export default DeliverExamModal;
