{"name": "pgs-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "typeorm": "typeorm-ts-node-esm", "migration:generate": "npm run typeorm -- migration:generate", "migration:create": "npm run typeorm -- migration:create", "migration:run:local": "pwsh -File run-migrations.ps1 -Action run -Environment local", "migration:run:dev": "pwsh -File run-migrations.ps1 -Action run -Environment dev", "migration:run:test": "pwsh -File run-migrations.ps1 -Action run -Environment test", "migration:run:qa": "pwsh -File run-migrations.ps1 -Action run -Environment qa", "migration:run:production": "pwsh -File run-migrations.ps1 -Action run -Environment production", "migration:revert:local": "pwsh -File run-migrations.ps1 -Action revert -Environment local", "migration:revert:dev": "pwsh -File run-migrations.ps1 -Action revert -Environment dev", "migration:revert:test": "pwsh -File run-migrations.ps1 -Action revert -Environment test", "migration:revert:qa": "pwsh -File run-migrations.ps1 -Action revert -Environment qa", "migration:revert:production": "pwsh -File run-migrations.ps1 -Action revert -Environment production", "migration:show:local": "pwsh -File run-migrations.ps1 -Action show -Environment local", "migration:show:dev": "pwsh -File run-migrations.ps1 -Action show -Environment dev", "migration:show:test": "pwsh -File run-migrations.ps1 -Action show -Environment test", "migration:show:qa": "pwsh -File run-migrations.ps1 -Action show -Environment qa", "migration:show:production": "pwsh -File run-migrations.ps1 -Action show -Environment production"}, "dependencies": {"@azure/identity": "^4.8.0", "@azure/service-bus": "^7.9.5", "@azure/storage-blob": "^12.23.0", "@microsoft/applicationinsights-web": "^3.3.0", "@microsoft/signalr": "^8.0.7", "@nextui-org/react": "^2.1.13", "@opentelemetry/exporter-jaeger": "^1.25.1", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-pdf-viewer/zoom": "^3.12.0", "@types/react-dropzone": "^5.1.0", "applicationinsights": "^3.2.1", "bowser": "^2.11.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "dayjs": "^1.11.10", "framer-motion": "^11.5.6", "html2canvas": "^1.4.1", "ioredis": "^5.3.2", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.1", "lucide-react": "^0.525.0", "mssql": "^11.0.1", "next": "^15.3.5", "next-auth": "^4.24.6", "next-i18next": "^14.0.3", "next-intl": "^3.25.3", "pdfjs-dist": "^3.11.174", "platform": "^1.3.6", "puppeteer": "^24.12.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.2.3", "react-icons": "^4.11.0", "react-tooltip": "^5.24.0", "react-transition-group": "^4.4.5", "reflect-metadata": "^0.2.2", "sharp": "^0.32.6", "swr": "^2.3.0", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "typeorm": "^0.3.21", "uuid": "^9.0.1"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20", "@types/platform": "^1.3.6", "@types/puppeteer": "^5.4.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/uuid": "^9.0.7", "autoprefixer": "^10", "daisyui": "^3.9.4", "eslint": "^8", "eslint-config-next": "^15.3.5", "postcss": "^8", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.2.2"}}